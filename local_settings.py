"""
Local development settings - use this for local development with SQLite.

Usage:
    python manage.py shell --settings=local_settings
    python manage.py runserver --settings=local_settings
"""

import os

# Clear database environment variables to force SQLite usage
os.environ.pop('DATABASE_HOST', None)
os.environ.pop('DATABASE_URL', None)
os.environ.pop('DATABASE_NAME', None)
os.environ.pop('DATABASE_USER', None)
os.environ.pop('DATABASE_SECRET_ARN', None)

# Clear all AWS-related environment variables BEFORE importing settings
# This prevents AWS services from being initialized during import
os.environ.pop('AWS_ACCESS_KEY_ID', None)
os.environ.pop('AWS_SECRET_ACCESS_KEY', None)
os.environ.pop('AWS_SESSION_TOKEN', None)
os.environ.pop('AWS_PROFILE', None)
os.environ.pop('AWS_STORAGE_BUCKET_NAME', None)
os.environ.pop('AWS_S3_REGION_NAME', None)
os.environ.pop('AWS_DEFAULT_REGION', None)
os.environ.pop('AWS_SQS_FILE_PROCESSING_QUEUE_URL', None)
os.environ.pop('AWS_SNS_TOPIC_ARN', None)
os.environ.pop('SNS_TOPIC_ARN', None)

# Set a dummy bucket name to prevent S3Service initialization errors
# This will be overridden below
os.environ['AWS_STORAGE_BUCKET_NAME'] = 'dummy-bucket-for-local-dev'

print("🧹 Cleared AWS database environment variables for local development")

# Now import the main settings
from settings import *

# Ensure we're using SQLite regardless
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

print("🗄️ Using local SQLite database for development")

# Additional development-specific settings  
DEBUG = True
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '*.ngrok.io', '*.ngrok-free.app'] 

# Disable AWS services for local development
DEFAULT_FILE_STORAGE = 'django.core.files.storage.FileSystemStorage'
AWS_STORAGE_BUCKET_NAME = None
AWS_ACCESS_KEY_ID = None
AWS_SECRET_ACCESS_KEY = None
AWS_SESSION_TOKEN = None
AWS_PROFILE = None

# Clear the dummy bucket name we set earlier
os.environ.pop('AWS_STORAGE_BUCKET_NAME', None)

print("🚫 Disabled all AWS services for local development")