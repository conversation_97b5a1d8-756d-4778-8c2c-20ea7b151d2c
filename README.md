# Chatbook Backend

A Django-based chat application

## 🚀 Quick Setup

To set up and run this backend in a new terminal:

```bash
cd /Users/<USER>/Cursor_Projects/chatbook-backend
source venv/bin/activate
python manage.py runserver --settings=local_settings
```

For database operations locally:
```bash
python manage.py migrate --settings=local_settings
python manage.py createsuperuser --settings=local_settings
```

### Avoiding AWS SSO Token Issues

If you see `botocore.exceptions.TokenRetrievalError: Token has expired` when running locally, use one of these solutions:

**Option 1: Use local_settings.py (Recommended)**
```bash
python manage.py runserver --settings=local_settings
```

**Option 2: Create a local .env file without AWS variables**
Create a `.env` file with only local development settings (remove AWS_PROFILE and other AWS variables):
```env
DATABASE_URL=sqlite:///db.sqlite3
SECRET_KEY=django-insecure-k99^83^3e(mlk8my-@n29uc*-3l(*840)d(u&=^lpia(1#*o
DEBUG=True
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
# Don't include AWS_PROFILE, AWS_STORAGE_BUCKET_NAME, etc. for local dev
```

## 🔐 Authentication & Login System Design

The authentication system has been refactored to use a modular architecture with enhanced security:

1. **User Identity Management**
   - UUID-based primary keys for global uniqueness
   - Database-level uniqueness constraints on both email and phone number fields
   - Properly validated phone numbers using PhoneNumberField
   - Integration with social login providers (Google, Apple, Facebook)

2. **Social Login Integration**
   - Unified identity mapping to prevent duplicate accounts
   - Support for linking multiple social accounts to a single user profile
   - Proper verification of email and phone numbers during social registration

3. **Improved Multi-Factor Authentication (MFA)**
   - Integration with django-otp for standardized implementation
   - Risk-based MFA triggers for new/unrecognized devices and unusual locations
   - Support for both email and SMS verification methods

4. **Enhanced Account Recovery**
   - Secure token generation with proper hashing and expiration
   - Social provider account recovery options
   - Privacy-preserving recovery processes

5. **Better Brute-Force Protection**
   - Integration with django-axes for advanced protection
   - Rate limiting with exponential backoff
   - Automatic account lockout with secure unlock mechanisms

For more details about the accounts implementation, see [accounts/README.md](accounts/README.md).

## Testing

### Overview
The project includes comprehensive automated testing covering unit tests, integration tests, and end-to-end (E2E) tests.

### Test Structure
```
tests/
├── unit/
│   ├── test_auth/              # Authentication unit tests
│   │   ├── test_validation.py  # Input validation tests
│   │   ├── test_tokens.py      # Token generation/validation tests
│   │   └── test_sessions.py    # Session management tests
│   └── test_models/            # Model unit tests
├── integration/
│   ├── test_auth_flows.py      # Authentication flow tests
│   ├── test_mfa.py            # Multi-factor authentication tests
│   └── test_social_auth.py    # Social authentication tests
└── e2e/
    ├── test_login_flows.py    # End-to-end login scenarios
    ├── test_account_recovery.py # Account recovery flows
    └── test_security.py       # Security features (rate limiting, lockouts)
```

### Running Tests

1. Run all tests:
```bash
python manage.py test
```

2. Run specific test categories:
```bash
# Run unit tests only
python manage.py test tests.unit

# Run integration tests only
python manage.py test tests.integration

# Run E2E tests only
python manage.py test tests.e2e
```

3. Run specific test file:
```bash
python manage.py test tests.unit.test_auth.test_validation
```

### Test Coverage
To run tests with coverage report:
```bash
coverage run manage.py test
coverage report
coverage html  # Generates HTML report in htmlcov/
```

### Test Categories

#### Unit Tests
- Input validation tests
- Token generation and validation
- Session management
- Model validation and methods
- Rate limiting logic

#### Integration Tests
- Complete authentication flows
- MFA integration
- Social authentication providers
- Account management operations

#### End-to-End Tests
- Email/password login flows
- Social login flows (Google, Apple, Facebook)
- MFA triggers and verification
- Account recovery processes
- Security features (rate limiting, account lockouts)

### Setting Up Test Environment

1. Create test database:
```bash
python manage.py migrate --settings=chatbook.settings.test
```

2. Configure test settings:
- Copy `.env.example` to `.env.test`
- Update test-specific variables in `.env.test`

3. Set up test social auth:
- Configure test OAuth credentials in settings
- Use mock social auth responses for testing

### Mock Data
Test fixtures are available in `tests/fixtures/`:
- `users.json`: Sample user data
- `social_accounts.json`: Mock social account data
- `mfa_settings.json`: MFA configuration data

### Continuous Integration
Tests are automatically run on:
- Every pull request
- Merges to main branch
- Nightly builds

### Writing New Tests
1. Follow the existing directory structure
2. Use appropriate base test classes:
   - `TestCase` for unit tests
   - `TransactionTestCase` for integration tests
   - `LiveServerTestCase` for E2E tests
3. Use fixtures for consistent test data
4. Mock external services appropriately
5. Include both positive and negative test cases

## Appointment System

This application uses a centralized Appointment model to handle all customer bookings and appointments.

Key components:
- **Appointment model**: Central model for all customer appointments
- **AppointmentService**: Through-table linking appointments to services with quantities, prices, and durations
- **AppointmentAddOn**: Through-table for add-on services attached to appointments
- **RecurringPattern**: Handles recurring appointment series

The system previously had separate Booking and Appointment models in different apps, but these have been consolidated into a single Appointment model in the appointments app for better maintainability and data consistency.